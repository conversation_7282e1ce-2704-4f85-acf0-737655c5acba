* {
margin: 0;
padding: 0;
box-sizing: border-box;
font-family: 'Gill Sans', '<PERSON> Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;
}

html, body{
height: 100%;
width:100%;
}

  ul {
    list-style: none;
  }
  #main{
    width: 100%;
    height: 100vh;
  }

  #container{
    width: 100%;
    height: 100%;
    background-image: url(./download\ \(42\).jpeg);
    background-position: center;
    background-size: cover;
  }

  #wrapper{
    display: flex;
    justify-content: center;
  }
  main{
    width: 30vw;
    height: 30vh;
    background-color: #ffffff88;
    backdrop-filter: 20px;
    border-radius: 10px;
    margin: 8vw 0;
    text-align: center;
  }
  main ul{
    /* padding: 3vw ; */
    margin: 6vw 0;
  }
  h1{
    text-align: center;
    font-family: 'Gill Sans', 'Gill Sans MT', <PERSON><PERSON>ri, 'Trebuchet MS', sans-serif;
    padding: 2vw 0;
  }

  main ul li:nth-of-type(1n) {
    background-color: #ffffff88;
    border-radius: 10px;
    padding: 1vw 2vw;
    margin: 10px;
    display: flex;
    justify-content: space-between;
    font-size: 1.3vw;
  }

  main input{
    width: 15vw;
    padding: 1vw 1vw;
    border-radius: 5px;
    border: 1px solid #00000057;
  }


  main button{
    padding: 1vw 1vw;
    border-radius: 5px;
    border: 1px solid #000;
  }