const btn = document.querySelector("button");
const input = document.querySelector("input");
const ul = document.querySelector("ul");
let isEditing = false;
let counter = 0;
let idToEdit = null;
let tasks = [];

btn.addEventListener("click", () => {
    if(input.value === ""){
        return;
    }

    if(!isEditing) {
        const obj = {
            id: ++counter,
            task: input.valule
            
        };
        tasks.push(obj);
    }

    else {
        tasks.find((t) =>
        t.id === idToEdit).task = input.value;
        isEditing = false;
        idToEdit = null;
    }

    input.value = "";
    printTasks();
     console.log(tasks);
});


function printTasks() {
    ul.innerHTML = "";
    tasks.forEach((ob) => {
        const li = document.createElement("li");
        const span = document.createElement("span");
        const editBtn = document.createElement("button")
        const delBtn = document.createElement("button")

        span.innerHTML = ob.task;

        //icons
        editBtn.classList.add("fa-solid", "fa-pen-to-square");
        delBtn.classList.add("fa-solid", "fa-trash", "delete-btn" );

        li.appendChild(span)
        li.appendChild(editBtn)
        li.appendChild(delBtn)
        ul.appendChild(li);
        
//---------
        editBtn.addEventListener("click", () => {
            input.value = ob.task;
            isEditing = true;
            idToEdit = ob.id;
        })

//---------
        delBtn.addEventListener("click", () => {
            tasks = tasks.filter((t) => t.id !== ob.id);
            printTasks();
        })

    })
}